import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:balshifa/models/dose.dart';
import 'package:balshifa/utils/time_formatter.dart';
import 'package:balshifa/services/sound_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:balshifa/constants/app_colors.dart';
import 'package:balshifa/constants/app_styles.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // للتحكم في عرض الإشعارات على الشاشة
  static BuildContext? _currentContext;

  Future<void> initialize() async {
    print('🔧 تهيئة خدمة الإشعارات...');

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);

    final bool? initialized = await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: onDidReceiveNotificationResponse,
    );

    if (initialized == true) {
      print('✅ تم تهيئة خدمة الإشعارات بنجاح');
    } else {
      print('❌ فشل في تهيئة خدمة الإشعارات');
    }

    // طلب الأذونات للإشعارات
    await _requestPermissions();
  }

  Future<void> _requestPermissions() async {
    try {
      final bool? result = await _notificationsPlugin
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.requestNotificationsPermission();

      if (result == true) {
        print('✅ تم منح أذونات الإشعارات');
      } else {
        print('❌ لم يتم منح أذونات الإشعارات');
      }
    } catch (e) {
      print('خطأ في طلب أذونات الإشعارات: $e');
    }
  }

  void onDidReceiveNotificationResponse(NotificationResponse details) {
    // معالجة الضغط على الإشعارات وأزرار الإجراءات
    print('تم الضغط على الإشعار: ${details.payload}');
    print('الإجراء: ${details.actionId}');

    if (details.actionId == 'taken') {
      // تم تناول الدواء - إلغاء الإشعار وإيقاف الصوت
      final notificationId = _extractNotificationId(details.payload);
      if (notificationId != null) {
        cancelNotification(notificationId);
        _stopNotificationActivity();
      }
    } else if (details.actionId == 'later') {
      // تذكير لاحقاً - إيقاف الصوت مؤقتاً
      _stopNotificationActivity();
      // يمكن إضافة منطق لإعادة التذكير بعد فترة
    }
  }

  int? _extractNotificationId(String? payload) {
    if (payload != null && payload.startsWith('dose_')) {
      try {
        return int.parse(payload.substring(5));
      } catch (e) {
        print('خطأ في استخراج ID الإشعار: $e');
      }
    }
    return null;
  }

  Future<void> _stopNotificationActivity() async {
    try {
      const platform = MethodChannel('notification_channel');
      await platform.invokeMethod('stopNotificationActivity');
    } catch (e) {
      print('خطأ في إيقاف النشاط: $e');
    }
  }

  Future<void> showDoseNotification(Dose dose, String medicineName) async {
    final timeString = TimeFormatter.formatTime12Hour(dose.scheduledDateTime);
    final notificationId = dose.id ?? 0;

    // إنشاء إشعار مع صوت في شريط الإشعارات
    const AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
      'dose_reminder_channel',
      'تذكير بالجرعات',
      channelDescription: 'تذكير بمواعيد الجرعات الدوائية',
      importance: Importance.max,
      priority: Priority.high,
      ongoing: true, // يجعل الإشعار دائمًا
      autoCancel: false,
      playSound: true, // تفعيل الصوت
      sound: RawResourceAndroidNotificationSound('notification'), // صوت مخصص
      actions: <AndroidNotificationAction>[
        AndroidNotificationAction(
          'taken',
          'تم التناول ✅',
          showsUserInterface: true,
        ),
        AndroidNotificationAction(
          'later',
          'تذكيرني لاحقاً ⏰',
          showsUserInterface: true,
        ),
      ],
    );

    const NotificationDetails notificationDetails =
        NotificationDetails(android: androidNotificationDetails);

    // إشعار في شريط الإشعارات
    await _notificationsPlugin.show(
      notificationId,
      'وقت الجرعة ⏰',
      'الدواء: $medicineName\nالوقت: $timeString',
      notificationDetails,
      payload: 'dose_${dose.id}',
    );

    // إظهار النافذة المنبثقة مع الصوت المستمر
    await _showFullScreenNotification(medicineName, timeString, notificationId);

    // تشغيل صوت قوي إضافي
    await SoundService.playAlarmWithVibration();
  }

  Future<void> cancelNotification(int notificationId) async {
    await _notificationsPlugin.cancel(notificationId);
  }

  Future<void> cancelAllNotifications() async {
    await _notificationsPlugin.cancelAll();
  }

  // تعيين السياق الحالي للتطبيق
  static void setContext(BuildContext context) {
    _currentContext = context;
  }

  // إظهار النافذة المنبثقة باستخدام Android Activity
  Future<void> _showFullScreenNotification(String medicineName, String time, int notificationId) async {
    try {
      const platform = MethodChannel('notification_channel');
      await platform.invokeMethod('showNotificationActivity', {
        'medicine_name': medicineName,
        'dose_time': time,
        'notification_id': notificationId,
      });
    } catch (e) {
      print('خطأ في عرض النافذة المنبثقة: $e');
      // fallback للطريقة القديمة إذا فشلت
      _showOnScreenNotificationFallback(medicineName, time);
    }
  }

  // النافذة المنبثقة الاحتياطية (داخل التطبيق فقط)
  void _showOnScreenNotificationFallback(String medicineName, String time) {
    if (_currentContext != null) {
      showDialog(
        context: _currentContext!,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          backgroundColor: AppColors.surface,
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [AppColors.warning, AppColors.warning.withOpacity(0.8)],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.alarm,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '⏰ وقت الجرعة!',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
            ],
          ),
          content: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.warning.withOpacity(0.1),
                  AppColors.warning.withOpacity(0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.warning.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'حان وقت تناول الدواء',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Icon(
                      Icons.medication,
                      color: AppColors.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'الدواء: $medicineName',
                      style: TextStyle(
                        fontSize: 16,
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      color: AppColors.secondary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'الوقت: $time',
                      style: TextStyle(
                        fontSize: 16,
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.warning.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.warning.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: AppColors.warning,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'لا تنس تناول الدواء في الوقت المحدد',
                          style: TextStyle(
                            color: AppColors.warning,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    icon: const Icon(Icons.snooze),
                    label: const Text('تذكيرني لاحقاً'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.secondary,
                      side: BorderSide(color: AppColors.secondary),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [AppColors.success, AppColors.success.withOpacity(0.8)],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.of(context).pop();
                        // إلغاء الإشعار من شريط الإشعارات أيضاً
                        cancelNotification(0); // سنحتاج لتمرير ID صحيح
                      },
                      icon: const Icon(Icons.check, color: Colors.white),
                      label: const Text(
                        'تم التناول',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }
  }
}
