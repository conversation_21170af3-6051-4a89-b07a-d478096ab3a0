import 'package:balshifa/models/patient.dart';
import 'package:balshifa/models/dose.dart';
import 'package:balshifa/models/medicine.dart';
import 'package:balshifa/screens/patient_screen.dart';
import 'package:balshifa/services/database_service.dart';
import 'package:balshifa/services/notification_service.dart';
import 'package:balshifa/services/scheduled_notification_service.dart';
import 'package:balshifa/services/sound_service.dart';
import 'package:balshifa/utils/dose_checker.dart';
import 'package:balshifa/widgets/patient_tile.dart';
import 'package:balshifa/constants/app_colors.dart';
import 'package:balshifa/constants/app_styles.dart';
import 'package:flutter/material.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  List<Patient> patients = [];

  @override
  void initState() {
    super.initState();
    _loadPatients();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // تعيين السياق للإشعارات
    NotificationService.setContext(context);
  }

  _loadPatients() async {
    final loadedPatients = await DatabaseService.instance.getAllPatients();
    setState(() {
      patients = loadedPatients;
    });
  }

  _addPatient() async {
    String patientName = '';
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: AppColors.primaryGradient,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.person_add,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Text(
              'إضافة مريض جديد',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.lightGreen,
            borderRadius: BorderRadius.circular(12),
          ),
          child: TextField(
            onChanged: (value) => patientName = value,
            decoration: InputDecoration(
              hintText: 'اسم المريض',
              hintStyle: TextStyle(color: AppColors.textHint),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.white,
              prefixIcon: Icon(Icons.person, color: AppColors.primary),
            ),
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textPrimary,
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 16,
              ),
            ),
          ),
          Container(
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: BorderRadius.circular(12),
            ),
            child: TextButton.icon(
              onPressed: () async {
                if (patientName.trim().isNotEmpty) {
                  final patient = Patient(name: patientName.trim());
                  await DatabaseService.instance.insertPatient(patient);
                  _loadPatients();
                  Navigator.pop(context);

                  // عرض رسالة نجاح
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تم إضافة المريض "${patientName.trim()}" بنجاح! ✅'),
                        backgroundColor: AppColors.success,
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    );
                  }
                }
              },
              icon: const Icon(Icons.save, color: Colors.white),
              label: const Text(
                'حفظ',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  _testNotification() async {
    // تشغيل الصوت الجديد القوي مباشرة
    await SoundService.playAlarmWithVibration();

    // إنشاء جرعة تجريبية لاختبار الإشعار (بدون صوت)
    final testDose = Dose(
      id: 999,
      medicineId: 1,
      scheduledDateTime: DateTime.now(),
      status: 'scheduled',
    );

    // عرض الإشعار (بدون صوت - الصوت من الخدمة الجديدة)
    await NotificationService().showDoseNotification(testDose, 'دواء تجريبي');

    // عرض رسالة تأكيد عصرية
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.notifications_active,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'تم إرسال إشعار تجريبي! 🔔\nتحقق من شريط الإشعارات',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: AppColors.success,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          margin: const EdgeInsets.all(16),
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  // دالة تشخيص لفحص حالة الجرعات المجدولة
  _diagnoseDoses() async {
    try {
      final db = DatabaseService.instance;
      final patients = await db.getAllPatients();

      int totalPatients = patients.length;
      int totalMedicines = 0;
      int totalScheduledDoses = 0;
      int upcomingDoses = 0;

      final now = DateTime.now();
      final upcoming = now.add(const Duration(hours: 1)); // الجرعات في الساعة القادمة

      for (final patient in patients) {
        final medicines = await db.getMedicinesByPatient(patient.id!);
        totalMedicines += medicines.length;

        for (final medicine in medicines) {
          final scheduledDoses = await db.getScheduledDoses(medicine.id!);
          totalScheduledDoses += scheduledDoses.length;

          for (final dose in scheduledDoses) {
            if (dose.scheduledDateTime.isAfter(now) && dose.scheduledDateTime.isBefore(upcoming)) {
              upcomingDoses++;
            }
          }
        }
      }

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تشخيص نظام الإشعارات 🔍'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('عدد المرضى: $totalPatients'),
                Text('عدد الأدوية: $totalMedicines'),
                Text('عدد الجرعات المجدولة: $totalScheduledDoses'),
                Text('الجرعات في الساعة القادمة: $upcomingDoses'),
                const SizedBox(height: 16),
                Text(
                  upcomingDoses > 0
                    ? '✅ يوجد جرعات قادمة - الإشعارات ستعمل'
                    : '⚠️ لا توجد جرعات قادمة - أضف دواء جديد',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: upcomingDoses > 0 ? Colors.green : Colors.orange,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التشخيص: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  // إنشاء جرعة تجريبية قريبة لاختبار النظام
  _createTestDose() async {
    try {
      final db = DatabaseService.instance;
      final patients = await db.getAllPatients();

      if (patients.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('يجب إضافة مريض أولاً'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // إنشاء دواء تجريبي
      final testMedicine = Medicine(
        patientId: patients.first.id!,
        name: 'دواء تجريبي للاختبار',
        type: 'مسكن',
        dosage: '1 حبة',
        timesPerDay: 1,
        durationDays: 1,
        firstDoseDateTime: DateTime.now().add(const Duration(minutes: 1)), // بعد دقيقة واحدة
      );

      final medicineId = await db.insertMedicine(testMedicine);

      // إنشاء جرعة تجريبية
      final testDose = Dose(
        medicineId: medicineId,
        scheduledDateTime: DateTime.now().add(const Duration(minutes: 1)),
        status: 'scheduled',
      );

      await db.insertDose(testDose);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء جرعة تجريبية! ستصل الإشعار خلال دقيقة واحدة 🔔'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 5),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء الجرعة التجريبية: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // إعادة تشغيل نظام التحقق من الجرعات
  _restartDoseChecker() {
    DoseChecker.stopChecking();
    DoseChecker.startChecking();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إعادة تشغيل نظام التحقق من الجرعات! 🔄'),
          backgroundColor: Colors.blue,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  // التحقق من أذونات الإشعارات
  _checkNotificationPermissions() async {
    try {
      // إعادة تهيئة خدمة الإشعارات
      await NotificationService().initialize();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم التحقق من أذونات الإشعارات! تحقق من وحدة التحكم للتفاصيل 📱'),
            backgroundColor: Colors.blue,
            duration: Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التحقق من الأذونات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // إعادة جدولة جميع الإشعارات
  _rescheduleAllNotifications() async {
    try {
      await ScheduledNotificationService().scheduleAllDoseNotifications();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إعادة جدولة جميع الإشعارات! 🔄\nستعمل الإشعارات حتى عند إغلاق التطبيق'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 5),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إعادة جدولة الإشعارات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // عرض الإشعارات المجدولة
  _showScheduledNotifications() async {
    try {
      await ScheduledNotificationService().printPendingNotifications();
      final pending = await ScheduledNotificationService().getPendingNotifications();

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('الإشعارات المجدولة 📅'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('عدد الإشعارات المجدولة: ${pending.length}'),
                const SizedBox(height: 16),
                if (pending.isNotEmpty) ...[
                  const Text('الإشعارات القادمة:'),
                  const SizedBox(height: 8),
                  ...pending.take(5).map((notification) =>
                    Text('• ${notification.title}', style: const TextStyle(fontSize: 12))
                  ),
                  if (pending.length > 5)
                    Text('... و ${pending.length - 5} إشعارات أخرى'),
                ] else
                  const Text('لا توجد إشعارات مجدولة'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في عرض الإشعارات المجدولة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // طلب إذن تجاهل تحسين البطارية
  _requestBatteryOptimization() async {
    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تحسين الإشعارات 🔋'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('لضمان عمل الصوت في جميع الأوقات:'),
              SizedBox(height: 16),
              Text('1. اذهب إلى إعدادات الجهاز'),
              Text('2. البطارية > تحسين البطارية'),
              Text('3. ابحث عن "بالشفا"'),
              Text('4. اختر "عدم التحسين"'),
              SizedBox(height: 16),
              Text('5. في إعدادات الإشعارات:'),
              Text('6. فعّل "تجاوز وضع عدم الإزعاج"'),
              Text('7. فعّل "إظهار على الشاشة المقفلة"'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('حسناً'),
            ),
          ],
        ),
      );
    }
  }

  // اختبار الصوت القوي
  _testLoudSound() async {
    await SoundService.playAlarmWithVibration();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تم تشغيل الصوت القوي! 🔊📳'),
          backgroundColor: Colors.orange,
          action: SnackBarAction(
            label: 'إيقاف',
            textColor: Colors.white,
            onPressed: () => SoundService.stopAlarmSound(),
          ),
          duration: const Duration(seconds: 10),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header عصري مع تدرج
              Container(
                width: double.infinity,
                margin: const EdgeInsets.all(20),
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: AppColors.primaryGradient,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withOpacity(0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'مرحباً بك في بالشفا! 👋',
                              style: TextStyle(
                                color: AppColors.textWhite,
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'نظام إدارة الأدوية والمرضى',
                              style: TextStyle(
                                color: AppColors.textWhite.withOpacity(0.9),
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: IconButton(
                                onPressed: _testNotification,
                                icon: const Icon(
                                  Icons.notifications_active,
                                  color: Colors.white,
                                  size: 24,
                                ),
                                tooltip: 'اختبار الإشعارات',
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: IconButton(
                                onPressed: _diagnoseDoses,
                                icon: const Icon(
                                  Icons.bug_report,
                                  color: Colors.white,
                                  size: 24,
                                ),
                                tooltip: 'تشخيص الإشعارات',
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: IconButton(
                                onPressed: _createTestDose,
                                icon: const Icon(
                                  Icons.schedule,
                                  color: Colors.white,
                                  size: 24,
                                ),
                                tooltip: 'إنشاء جرعة تجريبية',
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconButton(
                            onPressed: _testLoudSound,
                            icon: const Icon(
                              Icons.volume_up,
                              color: Colors.white,
                              size: 24,
                            ),
                            tooltip: 'اختبار الصوت القوي',
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    // صف إضافي للأزرار المساعدة
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconButton(
                            onPressed: _restartDoseChecker,
                            icon: const Icon(
                              Icons.refresh,
                              color: Colors.white,
                              size: 24,
                            ),
                            tooltip: 'إعادة تشغيل نظام التحقق',
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconButton(
                            onPressed: _checkNotificationPermissions,
                            icon: const Icon(
                              Icons.security,
                              color: Colors.white,
                              size: 24,
                            ),
                            tooltip: 'التحقق من الأذونات',
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconButton(
                            onPressed: _rescheduleAllNotifications,
                            icon: const Icon(
                              Icons.schedule_send,
                              color: Colors.white,
                              size: 24,
                            ),
                            tooltip: 'إعادة جدولة الإشعارات',
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconButton(
                            onPressed: _showScheduledNotifications,
                            icon: const Icon(
                              Icons.list_alt,
                              color: Colors.white,
                              size: 24,
                            ),
                            tooltip: 'عرض الإشعارات المجدولة',
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconButton(
                            onPressed: _requestBatteryOptimization,
                            icon: const Icon(
                              Icons.battery_saver,
                              color: Colors.white,
                              size: 24,
                            ),
                            tooltip: 'إعدادات البطارية للصوت',
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.calendar_today,
                                color: Colors.white,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 12),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.people,
                                color: Colors.white,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '${patients.length} مريض',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // قائمة المرضى
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  child: patients.isEmpty
                      ? _buildEmptyState()
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'قائمة المرضى',
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Expanded(
                              child: ListView.builder(
                                itemCount: patients.length,
                                itemBuilder: (context, index) {
                                  final patient = patients[index];
                                  return Container(
                                    margin: const EdgeInsets.only(bottom: 12),
                                    decoration: BoxDecoration(
                                      color: AppColors.surface,
                                      borderRadius: BorderRadius.circular(16),
                                      boxShadow: [
                                        BoxShadow(
                                          color: AppColors.shadow,
                                          blurRadius: 10,
                                          offset: const Offset(0, 4),
                                        ),
                                      ],
                                    ),
                                    child: PatientTile(
                                      patient: patient,
                                      onTap: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => PatientScreen(
                                              patient: patient,
                                            ),
                                          ),
                                        ).then((_) => _loadPatients());
                                      },
                                      onDelete: () async {
                                        await DatabaseService.instance
                                            .deletePatient(patient.id!);
                                        _loadPatients();
                                      },
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: Container(
        decoration: BoxDecoration(
          gradient: AppColors.primaryGradient,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withOpacity(0.4),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: FloatingActionButton(
          onPressed: _addPatient,
          backgroundColor: Colors.transparent,
          elevation: 0,
          child: const Icon(
            Icons.add,
            color: Colors.white,
            size: 28,
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: AppColors.lightGreen,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              Icons.people_outline,
              size: 64,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد مرضى مضافين',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على زر + لإضافة مريض جديد',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
}
