import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:balshifa/services/database_service.dart';
import 'package:balshifa/models/dose.dart';
import 'package:balshifa/utils/time_formatter.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import 'package:flutter/services.dart';

class AlarmService {
  static final AlarmService _instance = AlarmService._internal();
  factory AlarmService() => _instance;
  AlarmService._internal();

  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // إعدادات الصوت
  String _selectedSound = 'alarm_default'; // الصوت الافتراضي
  
  // قائمة الأصوات المتاحة
  static const List<Map<String, String>> availableSounds = [
    {'id': 'alarm_default', 'name': 'منبه افتراضي'},
    {'id': 'alarm_classic', 'name': 'منبه كلاسيكي'},
    {'id': 'alarm_gentle', 'name': 'منبه هادئ'},
    {'id': 'alarm_loud', 'name': 'منبه قوي'},
    {'id': 'alarm_beep', 'name': 'صفارة'},
  ];

  Future<void> initialize() async {
    print('🚨 تهيئة نظام المنبه...');
    
    // تهيئة المناطق الزمنية
    tz.initializeTimeZones();
    
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);

    await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onAlarmResponse,
    );
    
    print('✅ تم تهيئة نظام المنبه بنجاح');
  }

  // معالجة استجابة المنبه
  void _onAlarmResponse(NotificationResponse details) async {
    print('🚨 تم استلام منبه: ${details.payload}');
    
    if (details.actionId == 'taken') {
      await _markDoseAsTaken(details.payload);
    } else if (details.actionId == 'snooze') {
      await _snoozeDose(details.payload);
    } else {
      // عند الضغط على الإشعار نفسه - إظهار النافذة المنبثقة
      await _showFullScreenAlarm(details.payload);
    }
  }

  // إظهار منبه ملء الشاشة
  Future<void> _showFullScreenAlarm(String? payload) async {
    try {
      if (payload == null) return;
      
      // استخراج معلومات الجرعة
      final parts = payload.split('|');
      if (parts.length < 3) return;
      
      final doseId = parts[0];
      final medicineName = parts[1];
      final doseTime = parts[2];
      
      // إظهار النافذة المنبثقة مع الصوت
      const platform = MethodChannel('alarm_channel');
      await platform.invokeMethod('showFullScreenAlarm', {
        'dose_id': doseId,
        'medicine_name': medicineName,
        'dose_time': doseTime,
        'sound': _selectedSound,
      });
      
    } catch (e) {
      print('❌ خطأ في إظهار المنبه: $e');
    }
  }

  // تأجيل الجرعة 5 دقائق
  Future<void> _snoozeDose(String? payload) async {
    try {
      if (payload == null) return;
      
      final parts = payload.split('|');
      if (parts.length < 3) return;
      
      final doseId = int.parse(parts[0]);
      final medicineName = parts[1];
      
      // جدولة منبه جديد بعد 5 دقائق
      final snoozeTime = DateTime.now().add(const Duration(minutes: 5));
      await _scheduleAlarm(doseId, medicineName, snoozeTime);
      
      print('⏰ تم تأجيل الجرعة 5 دقائق');
    } catch (e) {
      print('❌ خطأ في تأجيل الجرعة: $e');
    }
  }

  // تسجيل الجرعة كمأخوذة
  Future<void> _markDoseAsTaken(String? payload) async {
    try {
      if (payload == null) return;
      
      final parts = payload.split('|');
      if (parts.length < 1) return;
      
      final doseId = int.parse(parts[0]);
      
      // تحديث قاعدة البيانات
      final db = DatabaseService.instance;
      final dose = await db.getDoseById(doseId);
      
      if (dose != null) {
        final takenDose = Dose(
          id: dose.id,
          medicineId: dose.medicineId,
          scheduledDateTime: dose.scheduledDateTime,
          takenDateTime: DateTime.now(),
          status: 'taken',
        );
        
        await db.updateDose(takenDose);
        print('✅ تم تسجيل الجرعة كمأخوذة');
      }
    } catch (e) {
      print('❌ خطأ في تسجيل الجرعة: $e');
    }
  }

  // جدولة منبه لجرعة واحدة (دالة عامة)
  Future<void> scheduleAlarmForDose(int doseId, String medicineName, DateTime alarmTime) async {
    await _scheduleAlarm(doseId, medicineName, alarmTime);
  }

  // جدولة منبه لجرعة (دالة داخلية)
  Future<void> _scheduleAlarm(int doseId, String medicineName, DateTime alarmTime) async {
    try {
      final timeString = TimeFormatter.formatTime12Hour(alarmTime);
      final payload = '$doseId|$medicineName|$timeString';
      
      // تحويل الوقت إلى المنطقة الزمنية المحلية
      final scheduledDate = tz.TZDateTime.from(alarmTime, tz.local);
      
      const AndroidNotificationDetails androidNotificationDetails =
          AndroidNotificationDetails(
        'dose_alarm_channel',
        'منبه الجرعات',
        channelDescription: 'منبه لتذكير بمواعيد الجرعات',
        importance: Importance.max,
        priority: Priority.high,
        fullScreenIntent: true,
        category: AndroidNotificationCategory.alarm,
        visibility: NotificationVisibility.public,
        playSound: true, // تفعيل الصوت في الإشعار أيضاً
        autoCancel: false,
        ongoing: false,
        actions: <AndroidNotificationAction>[
          AndroidNotificationAction(
            'taken',
            'تم أخذ الجرعة ✅',
            showsUserInterface: false,
          ),
          AndroidNotificationAction(
            'snooze',
            'ذكرني بعد 5 دقائق ⏰',
            showsUserInterface: false,
          ),
        ],
      );

      const NotificationDetails notificationDetails =
          NotificationDetails(android: androidNotificationDetails);

      await _notificationsPlugin.zonedSchedule(
        doseId,
        'وقت الجرعة! ⏰',
        'الدواء: $medicineName\nالوقت: $timeString',
        scheduledDate,
        notificationDetails,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        payload: payload,
      );

      print('🚨 تم جدولة منبه للدواء: $medicineName في $alarmTime');

      // إذا كان الوقت قريب جداً (أقل من 10 ثوان)، اعرض النافذة مباشرة
      final now = DateTime.now();
      final difference = alarmTime.difference(now).inSeconds;
      if (difference <= 10 && difference >= 0) {
        print('⏰ الوقت قريب جداً - عرض النافذة مباشرة');
        await _showFullScreenAlarm(payload);
      }
    } catch (e) {
      print('❌ خطأ في جدولة المنبه: $e');
    }
  }

  // جدولة جميع منبهات الجرعات
  Future<void> scheduleAllDoseAlarms() async {
    try {
      print('🚨 بدء جدولة جميع منبهات الجرعات...');
      
      // إلغاء جميع المنبهات السابقة
      await _notificationsPlugin.cancelAll();
      
      final db = DatabaseService.instance;
      final patients = await db.getAllPatients();
      
      int totalScheduled = 0;
      final now = DateTime.now();
      
      for (final patient in patients) {
        final medicines = await db.getMedicinesByPatient(patient.id!);
        
        for (final medicine in medicines) {
          final scheduledDoses = await db.getScheduledDoses(medicine.id!);
          
          for (final dose in scheduledDoses) {
            // جدولة المنبهات للجرعات المستقبلية فقط
            if (dose.scheduledDateTime.isAfter(now) && dose.id != null) {
              await _scheduleAlarm(dose.id!, medicine.name, dose.scheduledDateTime);
              totalScheduled++;
            }
          }
        }
      }
      
      print('✅ تم جدولة $totalScheduled منبه للجرعات المستقبلية');
    } catch (e) {
      print('❌ خطأ في جدولة المنبهات: $e');
    }
  }

  // تغيير الصوت المحدد
  void setSelectedSound(String soundId) {
    _selectedSound = soundId;
    print('🔊 تم تغيير الصوت إلى: $soundId');
  }

  // الحصول على الصوت المحدد
  String get selectedSound => _selectedSound;

  // إلغاء منبه معين
  Future<void> cancelAlarm(int doseId) async {
    await _notificationsPlugin.cancel(doseId);
    print('❌ تم إلغاء المنبه: $doseId');
  }

  // إلغاء جميع المنبهات
  Future<void> cancelAllAlarms() async {
    await _notificationsPlugin.cancelAll();
    print('❌ تم إلغاء جميع المنبهات');
  }
}
