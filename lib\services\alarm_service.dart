import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:balshifa/services/database_service.dart';
import 'package:balshifa/models/dose.dart';
import 'package:balshifa/utils/time_formatter.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import 'package:flutter/services.dart';

class AlarmService {
  static final AlarmService _instance = AlarmService._internal();
  factory AlarmService() => _instance;
  AlarmService._internal();

  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // إعدادات الصوت
  String _selectedSound = 'alarm_default'; // الصوت الافتراضي

  // نظام مراقبة إضافي
  Timer? _backgroundChecker;
  
  // قائمة الأصوات المتاحة
  static const List<Map<String, String>> availableSounds = [
    {'id': 'alarm_default', 'name': 'منبه افتراضي'},
    {'id': 'alarm_classic', 'name': 'منبه كلاسيكي'},
    {'id': 'alarm_gentle', 'name': 'منبه هادئ'},
    {'id': 'alarm_loud', 'name': 'منبه قوي'},
    {'id': 'alarm_beep', 'name': 'صفارة'},
  ];

  Future<void> initialize() async {
    print('🚨 تهيئة نظام المنبه...');
    
    // تهيئة المناطق الزمنية
    tz.initializeTimeZones();
    
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);

    await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onAlarmResponse,
      onDidReceiveBackgroundNotificationResponse: onNotificationReceived,
    );

    // إنشاء قناة إشعارات مخصصة للمنبه
    await _createAlarmNotificationChannel();

    // طلب الأذونات المطلوبة
    await _requestPermissions();

    // بدء نظام المراقبة الإضافي
    _startBackgroundChecker();

    print('✅ تم تهيئة نظام المنبه بنجاح');
  }

  // بدء نظام مراقبة إضافي للتأكد من عرض الشاشة
  void _startBackgroundChecker() {
    print('🔄 بدء نظام المراقبة الإضافي...');

    _backgroundChecker = Timer.periodic(const Duration(minutes: 1), (timer) async {
      await _checkForDueAlarms();
    });
  }

  // فحص الجرعات المستحقة وعرض الشاشة إذا لزم الأمر
  Future<void> _checkForDueAlarms() async {
    try {
      final now = DateTime.now();
      final db = DatabaseService.instance;
      final patients = await db.getAllPatients();

      for (final patient in patients) {
        final medicines = await db.getMedicinesByPatient(patient.id!);

        for (final medicine in medicines) {
          final scheduledDoses = await db.getScheduledDoses(medicine.id!);

          for (final dose in scheduledDoses) {
            // إذا كانت الجرعة متأخرة بـ 0-2 دقيقة، اعرض الشاشة
            final difference = now.difference(dose.scheduledDateTime).inMinutes;
            if (difference >= 0 && difference <= 2 && dose.status == 'scheduled') {
              print('🚨 جرعة متأخرة: ${medicine.name} - عرض الشاشة الآن!');
              final timeString = TimeFormatter.formatTime12Hour(dose.scheduledDateTime);
              final payload = '${dose.id}|${medicine.name}|$timeString';
              await _showFullScreenAlarm(payload);
              break; // عرض شاشة واحدة فقط في كل مرة
            }
          }
        }
      }
    } catch (e) {
      print('❌ خطأ في فحص الجرعات المستحقة: $e');
    }
  }

  // إنشاء قناة إشعارات مخصصة للمنبه
  Future<void> _createAlarmNotificationChannel() async {
    try {
      final AndroidNotificationChannel channel = AndroidNotificationChannel(
        'dose_alarm_channel',
        'منبه الجرعات',
        description: 'منبه لتذكير بمواعيد الجرعات',
        importance: Importance.max,
        playSound: true,
        enableVibration: true,
        vibrationPattern: Int64List.fromList([0, 1000, 500, 1000, 500, 1000]),
        showBadge: true,
        enableLights: true,
        ledColor: const Color.fromARGB(255, 255, 0, 0), // ضوء أحمر
      );

      await _notificationsPlugin
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);

      print('📢 تم إنشاء قناة إشعارات المنبه');
    } catch (e) {
      print('❌ خطأ في إنشاء قناة الإشعارات: $e');
    }
  }

  // طلب جميع الأذونات المطلوبة
  Future<void> _requestPermissions() async {
    try {
      print('📱 طلب الأذونات المطلوبة...');

      // طلب إذن الإشعارات
      final notificationPermission = await _notificationsPlugin
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.requestNotificationsPermission();

      print('🔔 إذن الإشعارات: ${notificationPermission ?? 'غير محدد'}');

      // طلب إذن جدولة الإشعارات الدقيقة
      final exactAlarmPermission = await _notificationsPlugin
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.requestExactAlarmsPermission();

      print('⏰ إذن المنبهات الدقيقة: ${exactAlarmPermission ?? 'غير محدد'}');

      // طلب إذن تجاهل تحسين البطارية
      await _requestBatteryOptimizationPermission();

      print('✅ تم طلب جميع الأذونات');
    } catch (e) {
      print('❌ خطأ في طلب الأذونات: $e');
    }
  }

  // طلب إذن تجاهل تحسين البطارية
  Future<void> _requestBatteryOptimizationPermission() async {
    try {
      const platform = MethodChannel('alarm_channel');
      await platform.invokeMethod('requestBatteryOptimization');
      print('🔋 تم طلب إذن تجاهل تحسين البطارية');
    } catch (e) {
      print('⚠️ لا يمكن طلب إذن تحسين البطارية: $e');
    }
  }

  // معالجة استجابة المنبه
  void _onAlarmResponse(NotificationResponse details) async {
    print('🚨 تم استلام منبه: ${details.payload}');
    print('🔍 نوع الاستجابة: ${details.actionId ?? 'ضغط على الإشعار'}');

    if (details.actionId == 'taken') {
      await _markDoseAsTaken(details.payload);
    } else if (details.actionId == 'snooze') {
      await _snoozeDose(details.payload);
    } else {
      // عند الضغط على الإشعار نفسه - إظهار الشاشة
      print('📱 عرض الشاشة المنبثقة...');
      await _showFullScreenAlarm(details.payload);
    }
  }

  // معالج إضافي للإشعارات عند وصولها (حتى لو لم يضغط المستخدم)
  static void onNotificationReceived(NotificationResponse details) async {
    print('📨 وصل إشعار جديد: ${details.payload}');

    // إظهار الشاشة مباشرة عند وصول الإشعار
    final alarmService = AlarmService();
    await alarmService._showFullScreenAlarm(details.payload);
  }

  // إظهار منبه ملء الشاشة
  Future<void> _showFullScreenAlarm(String? payload) async {
    try {
      if (payload == null) return;
      
      // استخراج معلومات الجرعة
      final parts = payload.split('|');
      if (parts.length < 3) return;
      
      final doseId = parts[0];
      final medicineName = parts[1];
      final doseTime = parts[2];
      
      // إظهار النافذة المنبثقة مع الصوت
      const platform = MethodChannel('alarm_channel');
      await platform.invokeMethod('showFullScreenAlarm', {
        'dose_id': doseId,
        'medicine_name': medicineName,
        'dose_time': doseTime,
        'sound': _selectedSound,
      });
      
    } catch (e) {
      print('❌ خطأ في إظهار المنبه: $e');
    }
  }

  // تأجيل الجرعة 5 دقائق
  Future<void> _snoozeDose(String? payload) async {
    try {
      if (payload == null) return;
      
      final parts = payload.split('|');
      if (parts.length < 3) return;
      
      final doseId = int.parse(parts[0]);
      final medicineName = parts[1];
      
      // جدولة منبه جديد بعد 5 دقائق
      final snoozeTime = DateTime.now().add(const Duration(minutes: 5));
      await _scheduleAlarm(doseId, medicineName, snoozeTime);
      
      print('⏰ تم تأجيل الجرعة 5 دقائق');
    } catch (e) {
      print('❌ خطأ في تأجيل الجرعة: $e');
    }
  }

  // تسجيل الجرعة كمأخوذة
  Future<void> _markDoseAsTaken(String? payload) async {
    try {
      if (payload == null) return;
      
      final parts = payload.split('|');
      if (parts.length < 1) return;
      
      final doseId = int.parse(parts[0]);
      
      // تحديث قاعدة البيانات
      final db = DatabaseService.instance;
      final dose = await db.getDoseById(doseId);
      
      if (dose != null) {
        final takenDose = Dose(
          id: dose.id,
          medicineId: dose.medicineId,
          scheduledDateTime: dose.scheduledDateTime,
          takenDateTime: DateTime.now(),
          status: 'taken',
        );
        
        await db.updateDose(takenDose);
        print('✅ تم تسجيل الجرعة كمأخوذة');
      }
    } catch (e) {
      print('❌ خطأ في تسجيل الجرعة: $e');
    }
  }

  // جدولة منبه لجرعة واحدة (دالة عامة)
  Future<void> scheduleAlarmForDose(int doseId, String medicineName, DateTime alarmTime) async {
    await _scheduleAlarm(doseId, medicineName, alarmTime);
  }

  // جدولة منبه لجرعة (دالة داخلية)
  Future<void> _scheduleAlarm(int doseId, String medicineName, DateTime alarmTime) async {
    try {
      final timeString = TimeFormatter.formatTime12Hour(alarmTime);
      final payload = '$doseId|$medicineName|$timeString';

      print('🚨 جدولة إشعار مع شاشة مباشرة للدواء: $medicineName في $alarmTime');

      // تحويل الوقت إلى المنطقة الزمنية المحلية
      final scheduledDate = tz.TZDateTime.from(alarmTime, tz.local);

      // إعداد الإشعار مع صوت منبه قوي ومستمر
      final AndroidNotificationDetails androidNotificationDetails =
          AndroidNotificationDetails(
        'dose_alarm_channel',
        'منبه الجرعات',
        channelDescription: 'منبه لتذكير بمواعيد الجرعات',
        importance: Importance.max,
        priority: Priority.high,
        fullScreenIntent: true, // هذا يجعل الشاشة تظهر مباشرة
        category: AndroidNotificationCategory.alarm,
        visibility: NotificationVisibility.public,
        playSound: true,
        // استخدام صوت المنبه الافتراضي للنظام
        // sound: RawResourceAndroidNotificationSound('alarm_sound'),
        enableVibration: true,
        vibrationPattern: Int64List.fromList([0, 1000, 500, 1000, 500, 1000]), // اهتزاز قوي
        autoCancel: false,
        ongoing: true, // يبقى الإشعار حتى يتم التعامل معه
        showWhen: true,
        when: null,
        usesChronometer: false,
        timeoutAfter: null, // لا ينتهي تلقائياً
        audioAttributesUsage: AudioAttributesUsage.alarm, // استخدام كصوت منبه
        actions: const <AndroidNotificationAction>[
          AndroidNotificationAction(
            'taken',
            'تم أخذ الجرعة ✅',
            showsUserInterface: true,
          ),
          AndroidNotificationAction(
            'snooze',
            'ذكرني بعد 5 دقائق ⏰',
            showsUserInterface: true,
          ),
        ],
      );

      final NotificationDetails notificationDetails =
          NotificationDetails(android: androidNotificationDetails);

      await _notificationsPlugin.zonedSchedule(
        doseId,
        '🚨 وقت الجرعة! اضغط هنا ⏰',
        'الدواء: $medicineName\nالوقت: $timeString\n👆 اضغط لفتح الشاشة',
        scheduledDate,
        notificationDetails,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        payload: payload,
      );

      print('✅ تم جدولة إشعار مع شاشة مباشرة للدواء: $medicineName');

      // إذا كان الوقت قريب جداً (أقل من 10 ثوان)، اعرض الشاشة مباشرة
      final now = DateTime.now();
      final difference = alarmTime.difference(now).inSeconds;
      if (difference <= 10 && difference >= 0) {
        print('⏰ الوقت قريب جداً - عرض الشاشة مباشرة');
        await _showFullScreenAlarm(payload);
      }
    } catch (e) {
      print('❌ خطأ في جدولة الإشعار: $e');
    }
  }



  // جدولة جميع منبهات الجرعات
  Future<void> scheduleAllDoseAlarms() async {
    try {
      print('🚨 بدء جدولة جميع منبهات الجرعات...');

      // إلغاء جميع الإشعارات السابقة
      await _notificationsPlugin.cancelAll();
      print('🗑️ تم إلغاء جميع الإشعارات السابقة');

      final db = DatabaseService.instance;
      final patients = await db.getAllPatients();
      print('👥 عدد المرضى: ${patients.length}');

      int totalScheduled = 0;
      int totalDoses = 0;
      final now = DateTime.now();
      print('⏰ الوقت الحالي: ${now.toString()}');

      for (final patient in patients) {
        final medicines = await db.getMedicinesByPatient(patient.id!);
        print('💊 المريض ${patient.name}: ${medicines.length} أدوية');

        for (final medicine in medicines) {
          final scheduledDoses = await db.getScheduledDoses(medicine.id!);
          totalDoses += scheduledDoses.length;
          print('📋 الدواء ${medicine.name}: ${scheduledDoses.length} جرعات مجدولة');

          for (final dose in scheduledDoses) {
            print('⏱️ جرعة في: ${dose.scheduledDateTime.toString()} (ID: ${dose.id})');

            // جدولة المنبهات للجرعات المستقبلية فقط
            if (dose.scheduledDateTime.isAfter(now) && dose.id != null) {
              print('✅ جدولة منبه للجرعة ${dose.id}');
              await _scheduleAlarm(dose.id!, medicine.name, dose.scheduledDateTime);
              totalScheduled++;
            } else {
              print('❌ تخطي الجرعة ${dose.id} - وقت ماضي أو ID فارغ');
            }
          }
        }
      }

      print('📊 إجمالي الجرعات: $totalDoses');
      print('✅ تم جدولة $totalScheduled منبه للجرعات المستقبلية');
    } catch (e) {
      print('❌ خطأ في جدولة المنبهات: $e');
    }
  }

  // تغيير الصوت المحدد
  void setSelectedSound(String soundId) {
    _selectedSound = soundId;
    print('🔊 تم تغيير الصوت إلى: $soundId');
  }

  // دالة عامة لعرض الشاشة مباشرة (يمكن استدعاؤها من أي مكان)
  static Future<void> showAlarmScreen(String payload) async {
    final alarmService = AlarmService();
    await alarmService._showFullScreenAlarm(payload);
  }

  // الحصول على الصوت المحدد
  String get selectedSound => _selectedSound;

  // إلغاء منبه معين
  Future<void> cancelAlarm(int doseId) async {
    await _notificationsPlugin.cancel(doseId);
    print('❌ تم إلغاء المنبه: $doseId');
  }

  // إلغاء جميع المنبهات
  Future<void> cancelAllAlarms() async {
    await _notificationsPlugin.cancelAll();
    print('❌ تم إلغاء جميع المنبهات');
  }
}
