package com.example.balshifa

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.media.MediaPlayer
import android.media.RingtoneManager
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import androidx.core.app.NotificationManagerCompat

class NotificationActivity : Activity() {
    private var mediaPlayer: MediaPlayer? = null
    private var notificationId: Int = 0
    private var stopReceiver: BroadcastReceiver? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // إعداد النافذة لتظهر فوق جميع التطبيقات
        window.addFlags(
            WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
            WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
            WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
        )
        
        // الحصول على البيانات من Intent
        val medicineName = intent.getStringExtra("medicine_name") ?: "دواء"
        val doseTime = intent.getStringExtra("dose_time") ?: "الآن"
        notificationId = intent.getIntExtra("notification_id", 0)
        
        // إنشاء Layout بسيط
        createLayout(medicineName, doseTime)
        
        // تشغيل الصوت المستمر
        startContinuousSound()

        // تسجيل BroadcastReceiver لإيقاف النشاط
        setupStopReceiver()
    }
    
    private fun createLayout(medicineName: String, doseTime: String) {
        // إنشاء Layout برمجياً
        val layout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(40, 40, 40, 40)
            setBackgroundColor(android.graphics.Color.WHITE)
        }
        
        // العنوان
        val titleText = TextView(this).apply {
            text = "⏰ وقت الجرعة!"
            textSize = 24f
            setTextColor(android.graphics.Color.RED)
            gravity = android.view.Gravity.CENTER
            setPadding(0, 0, 0, 20)
        }
        
        // اسم الدواء
        val medicineText = TextView(this).apply {
            text = "الدواء: $medicineName"
            textSize = 18f
            setTextColor(android.graphics.Color.BLACK)
            setPadding(0, 10, 0, 10)
        }
        
        // وقت الجرعة
        val timeText = TextView(this).apply {
            text = "الوقت: $doseTime"
            textSize = 18f
            setTextColor(android.graphics.Color.BLACK)
            setPadding(0, 10, 0, 20)
        }
        
        // رسالة تحذيرية
        val warningText = TextView(this).apply {
            text = "⚠️ لا تنس تناول الدواء في الوقت المحدد"
            textSize = 16f
            setTextColor(android.graphics.Color.parseColor("#D32F2F"))
            gravity = android.view.Gravity.CENTER
            setPadding(20, 20, 20, 30)
            setBackgroundColor(android.graphics.Color.parseColor("#FFEBEE"))
        }
        
        // زر تم التناول
        val takenButton = Button(this).apply {
            text = "تم التناول ✅"
            textSize = 16f
            setBackgroundColor(android.graphics.Color.GREEN)
            setTextColor(android.graphics.Color.WHITE)
            setPadding(20, 15, 20, 15)
            setOnClickListener {
                stopSound()
                cancelNotification()
                finish()
            }
        }
        
        // زر تذكير لاحقاً
        val laterButton = Button(this).apply {
            text = "تذكيرني لاحقاً ⏰"
            textSize = 16f
            setBackgroundColor(android.graphics.Color.parseColor("#FF9800"))
            setTextColor(android.graphics.Color.WHITE)
            setPadding(20, 15, 20, 15)
            setOnClickListener {
                stopSound()
                // يمكن إضافة منطق لإعادة التذكير لاحقاً
                finish()
            }
        }
        
        // إضافة العناصر للـ Layout
        layout.addView(titleText)
        layout.addView(medicineText)
        layout.addView(timeText)
        layout.addView(warningText)
        layout.addView(takenButton)
        layout.addView(laterButton)
        
        setContentView(layout)
    }
    
    private fun startContinuousSound() {
        try {
            // محاولة تشغيل الصوت المخصص أولاً
            val customSoundUri = Uri.parse("android.resource://$packageName/raw/notification")
            mediaPlayer = MediaPlayer.create(this, customSoundUri)
            
            if (mediaPlayer == null) {
                // إذا فشل الصوت المخصص، استخدم صوت الإنذار الافتراضي
                val defaultUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM)
                mediaPlayer = MediaPlayer.create(this, defaultUri)
            }
            
            mediaPlayer?.apply {
                isLooping = true // تكرار مستمر
                start()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    private fun stopSound() {
        mediaPlayer?.apply {
            if (isPlaying) {
                stop()
            }
            release()
        }
        mediaPlayer = null
    }
    
    private fun cancelNotification() {
        val notificationManager = NotificationManagerCompat.from(this)
        notificationManager.cancel(notificationId)
    }
    
    private fun setupStopReceiver() {
        stopReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                if (intent?.action == "STOP_NOTIFICATION_ACTIVITY") {
                    stopSound()
                    finish()
                }
            }
        }

        val filter = IntentFilter("STOP_NOTIFICATION_ACTIVITY")
        registerReceiver(stopReceiver, filter)
    }

    override fun onDestroy() {
        super.onDestroy()
        stopSound()
        stopReceiver?.let {
            unregisterReceiver(it)
        }
    }

    override fun onBackPressed() {
        // منع إغلاق النافذة بزر الرجوع
        // يجب الضغط على أحد الأزرار
    }
}
