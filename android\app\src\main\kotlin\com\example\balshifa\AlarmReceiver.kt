package com.example.balshifa

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import io.flutter.plugin.common.MethodChannel
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.dart.DartExecutor

class AlarmReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        Log.d("AlarmReceiver", "تم استلام منبه!")
        
        val payload = intent.getStringExtra("payload") ?: ""
        Log.d("AlarmReceiver", "البيانات: $payload")
        
        // إظهار نشاط المنبه
        val alarmIntent = Intent(context, AlarmActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            putExtra("payload", payload)
        }
        
        try {
            context.startActivity(alarmIntent)
            Log.d("AlarmReceiver", "تم بدء نشاط المنبه")
        } catch (e: Exception) {
            Log.e("AlarmReceiver", "خطأ في بدء نشاط المنبه: ${e.message}")
        }
    }
}
