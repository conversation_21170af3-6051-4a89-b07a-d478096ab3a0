import 'package:flutter/services.dart';

class SoundService {
  static const MethodChannel _channel = MethodChannel('sound_service');

  // تشغيل صوت قوي للإشعار
  static Future<void> playAlarmSound() async {
    try {
      await _channel.invokeMethod('playAlarmSound');
      print('🔊 تم تشغيل صوت المنبه');
    } catch (e) {
      print('❌ خطأ في تشغيل الصوت: $e');
    }
  }

  // إيقاف الصوت
  static Future<void> stopAlarmSound() async {
    try {
      await _channel.invokeMethod('stopAlarmSound');
      print('🔇 تم إيقاف صوت المنبه');
    } catch (e) {
      print('❌ خطأ في إيقاف الصوت: $e');
    }
  }

  // تشغيل صوت مع اهتزاز
  static Future<void> playAlarmWithVibration() async {
    try {
      await _channel.invokeMethod('playAlarmWithVibration');
      print('🔊📳 تم تشغيل صوت المنبه مع الاهتزاز');
    } catch (e) {
      print('❌ خطأ في تشغيل الصوت والاهتزاز: $e');
    }
  }
}
