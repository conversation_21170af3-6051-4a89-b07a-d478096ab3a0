import 'dart:async';
import 'package:balshifa/services/database_service.dart';
import 'package:balshifa/services/notification_service.dart';
import 'package:balshifa/models/dose.dart';

class DoseChecker {
  static Timer? _timer;

  static void startChecking() {
    print('🚀 بدء نظام التحقق من الجرعات...');

    // التحقق الأولي فوراً
    _checkDoses();

    // التحقق كل دقيقة واحدة (بدلاً من 5 دقائق للاختبار)
    _timer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkDoses();
    });

    print('✅ تم تشغيل نظام التحقق من الجرعات - سيتم التحقق كل دقيقة');
  }

  static void stopChecking() {
    _timer?.cancel();
  }

  static Future<void> _checkDoses() async {
    try {
      print('🔍 بدء التحقق من الجرعات...');
      final db = DatabaseService.instance;
      final patients = await db.getAllPatients();

      print('عدد المرضى: ${patients.length}');

      int totalDosesChecked = 0;
      int notificationsShown = 0;

      for (final patient in patients) {
        final medicines = await db.getMedicinesByPatient(patient.id!);
        print('المريض ${patient.name}: ${medicines.length} أدوية');

        for (final medicine in medicines) {
          final scheduledDoses = await db.getScheduledDoses(medicine.id!);
          print('الدواء ${medicine.name}: ${scheduledDoses.length} جرعات مجدولة');

          totalDosesChecked += scheduledDoses.length;

          for (final dose in scheduledDoses) {
            if (_shouldShowNotification(dose)) {
              print('📢 إرسال إشعار للدواء: ${medicine.name}');
              await NotificationService().showDoseNotification(dose, medicine.name);
              notificationsShown++;
              // التحقق من الجرعة المفقودة بعد ساعة
              _scheduleMissedCheck(dose, db);
            }
          }
        }
      }

      print('✅ انتهى التحقق - فحص $totalDosesChecked جرعة، أرسل $notificationsShown إشعار');
    } catch (e) {
      print('❌ خطأ في التحقق من الجرعات: $e');
    }
  }

  static bool _shouldShowNotification(Dose dose) {
    final now = DateTime.now();
    final doseTime = dose.scheduledDateTime;

    // التحقق من أن الجرعة مجدولة
    if (dose.status != 'scheduled') {
      return false;
    }

    // إذا كان الوقت قد حان (ضمن 2 دقائق قبل أو بعد)
    final difference = doseTime.difference(now).inMinutes;
    final shouldShow = difference >= -2 && difference <= 2;

    // طباعة معلومات التشخيص
    if (shouldShow) {
      print('🔔 وقت الجرعة! الفرق: $difference دقيقة');
      print('الوقت المجدول: ${doseTime.toString()}');
      print('الوقت الحالي: ${now.toString()}');
    }

    return shouldShow;
  }

  static void _scheduleMissedCheck(Dose dose, DatabaseService db) {
    Future.delayed(const Duration(hours: 1), () async {
      final updatedDose = await db.getDoseById(dose.id!);
      if (updatedDose != null && updatedDose.status == 'scheduled') {
        // تحديث إلى مفقودة
        final missedDose = Dose(
          id: updatedDose.id,
          medicineId: updatedDose.medicineId,
          scheduledDateTime: updatedDose.scheduledDateTime,
          status: 'missed',
        );
        await db.updateDose(missedDose);
      }
    });
  }
}
