import 'dart:async';
import 'package:balshifa/services/database_service.dart';
import 'package:balshifa/services/notification_service.dart';
import 'package:balshifa/models/dose.dart';

class DoseChecker {
  static Timer? _timer;

  static void startChecking() {
    // التحقق كل 5 دقائق
    _timer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _checkDoses();
    });
  }

  static void stopChecking() {
    _timer?.cancel();
  }

  static Future<void> _checkDoses() async {
    try {
      final db = DatabaseService.instance;
      final patients = await db.getAllPatients();

      for (final patient in patients) {
        final medicines = await db.getMedicinesByPatient(patient.id!);

        for (final medicine in medicines) {
          final scheduledDoses = await db.getScheduledDoses(medicine.id!);

          for (final dose in scheduledDoses) {
            if (_shouldShowNotification(dose)) {
              await NotificationService().showDoseNotification(dose, medicine.name);
              // التحقق من الجرعة المفقودة بعد ساعة
              _scheduleMissedCheck(dose, db);
            }
          }
        }
      }
    } catch (e) {
      print('خطأ في التحقق من الجرعات: $e');
    }
  }

  static bool _shouldShowNotification(Dose dose) {
    final now = DateTime.now();
    final doseTime = dose.scheduledDateTime;
    
    // إذا كان الوقت قد حان (ضمن 5 دقائق قبل أو بعد)
    final difference = doseTime.difference(now).inMinutes.abs();
    return difference <= 5 && dose.status == 'scheduled';
  }

  static void _scheduleMissedCheck(Dose dose, DatabaseService db) {
    Future.delayed(const Duration(hours: 1), () async {
      final updatedDose = await db.getDoseById(dose.id!);
      if (updatedDose != null && updatedDose.status == 'scheduled') {
        // تحديث إلى مفقودة
        final missedDose = Dose(
          id: updatedDose.id,
          medicineId: updatedDose.medicineId,
          scheduledDateTime: updatedDose.scheduledDateTime,
          status: 'missed',
        );
        await db.updateDose(missedDose);
      }
    });
  }
}
