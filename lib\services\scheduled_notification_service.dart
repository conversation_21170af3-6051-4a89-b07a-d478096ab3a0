import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:balshifa/services/database_service.dart';
import 'package:balshifa/models/dose.dart';
import 'package:balshifa/utils/time_formatter.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;

class ScheduledNotificationService {
  static final ScheduledNotificationService _instance = ScheduledNotificationService._internal();
  factory ScheduledNotificationService() => _instance;
  ScheduledNotificationService._internal();

  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  Future<void> initialize() async {
    print('🔧 تهيئة خدمة الإشعارات المجدولة...');

    // تهيئة المناطق الزمنية
    tz.initializeTimeZones();

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);

    await _notificationsPlugin.initialize(initializationSettings);

    // إنشاء قناة إشعارات مخصصة مع صوت عالي
    await _createNotificationChannel();

    print('✅ تم تهيئة خدمة الإشعارات المجدولة بنجاح');
  }

  // إنشاء قناة إشعارات مخصصة
  Future<void> _createNotificationChannel() async {
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'dose_reminder_channel',
      'تذكير بالجرعات',
      description: 'تذكير بمواعيد الجرعات الدوائية',
      importance: Importance.max,
      playSound: true,
      // استخدام صوت المنبه الافتراضي
      sound: null,
      enableVibration: true,
      showBadge: true,
      enableLights: true,
    );

    await _notificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);

    print('✅ تم إنشاء قناة الإشعارات مع الصوت');
  }

  // جدولة جميع الإشعارات للجرعات المستقبلية
  Future<void> scheduleAllDoseNotifications() async {
    try {
      print('📅 بدء جدولة جميع الإشعارات...');
      
      // إلغاء جميع الإشعارات المجدولة السابقة
      await _notificationsPlugin.cancelAll();
      
      final db = DatabaseService.instance;
      final patients = await db.getAllPatients();
      
      int totalScheduled = 0;
      final now = DateTime.now();
      
      for (final patient in patients) {
        final medicines = await db.getMedicinesByPatient(patient.id!);
        
        for (final medicine in medicines) {
          final scheduledDoses = await db.getScheduledDoses(medicine.id!);
          
          for (final dose in scheduledDoses) {
            // جدولة الإشعارات للجرعات المستقبلية فقط
            if (dose.scheduledDateTime.isAfter(now)) {
              await _scheduleNotificationForDose(dose, medicine.name);
              totalScheduled++;
            }
          }
        }
      }
      
      print('✅ تم جدولة $totalScheduled إشعار للجرعات المستقبلية');
    } catch (e) {
      print('❌ خطأ في جدولة الإشعارات: $e');
    }
  }

  // جدولة إشعار لجرعة واحدة
  Future<void> _scheduleNotificationForDose(Dose dose, String medicineName) async {
    try {
      final timeString = TimeFormatter.formatTime12Hour(dose.scheduledDateTime);
      final notificationId = dose.id ?? 0;
      
      // تحويل الوقت إلى المنطقة الزمنية المحلية
      final scheduledDate = tz.TZDateTime.from(dose.scheduledDateTime, tz.local);
      
      const AndroidNotificationDetails androidNotificationDetails =
          AndroidNotificationDetails(
        'dose_reminder_channel',
        'تذكير بالجرعات',
        channelDescription: 'تذكير بمواعيد الجرعات الدوائية',
        importance: Importance.max,
        priority: Priority.high,
        ongoing: false, // تغيير إلى false لتمكين الصوت
        autoCancel: false,
        playSound: true,
        // استخدام صوت المنبه الافتراضي للنظام (أقوى من الصوت المخصص)
        sound: null, // سيستخدم الصوت الافتراضي
        enableVibration: true,
        fullScreenIntent: true, // إضافة نافذة ملء الشاشة
        category: AndroidNotificationCategory.alarm, // تصنيف كمنبه
        visibility: NotificationVisibility.public, // مرئي على الشاشة المقفلة
        actions: <AndroidNotificationAction>[
          AndroidNotificationAction(
            'taken',
            'تم التناول ✅',
            showsUserInterface: true,
          ),
          AndroidNotificationAction(
            'later',
            'تذكيرني لاحقاً ⏰',
            showsUserInterface: true,
          ),
        ],
      );

      const NotificationDetails notificationDetails =
          NotificationDetails(android: androidNotificationDetails);

      await _notificationsPlugin.zonedSchedule(
        notificationId,
        'وقت الجرعة ⏰',
        'الدواء: $medicineName\nالوقت: $timeString\n⚠️ لا تنس تناول الدواء!',
        scheduledDate,
        notificationDetails,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        payload: 'dose_${dose.id}',
      );
      
      print('📅 تم جدولة إشعار للدواء: $medicineName في ${dose.scheduledDateTime}');
    } catch (e) {
      print('❌ خطأ في جدولة إشعار الجرعة: $e');
    }
  }

  // جدولة إشعار لدواء جديد
  Future<void> scheduleNotificationsForMedicine(int medicineId, String medicineName) async {
    try {
      final db = DatabaseService.instance;
      final scheduledDoses = await db.getScheduledDoses(medicineId);
      final now = DateTime.now();
      
      int scheduled = 0;
      for (final dose in scheduledDoses) {
        if (dose.scheduledDateTime.isAfter(now)) {
          await _scheduleNotificationForDose(dose, medicineName);
          scheduled++;
        }
      }
      
      print('✅ تم جدولة $scheduled إشعار للدواء: $medicineName');
    } catch (e) {
      print('❌ خطأ في جدولة إشعارات الدواء: $e');
    }
  }

  // إلغاء إشعارات دواء معين
  Future<void> cancelNotificationsForMedicine(int medicineId) async {
    try {
      final db = DatabaseService.instance;
      final doses = await db.getScheduledDoses(medicineId);
      
      for (final dose in doses) {
        if (dose.id != null) {
          await _notificationsPlugin.cancel(dose.id!);
        }
      }
      
      print('✅ تم إلغاء إشعارات الدواء: $medicineId');
    } catch (e) {
      print('❌ خطأ في إلغاء إشعارات الدواء: $e');
    }
  }

  // إلغاء إشعار جرعة واحدة
  Future<void> cancelNotification(int notificationId) async {
    await _notificationsPlugin.cancel(notificationId);
    print('✅ تم إلغاء الإشعار: $notificationId');
  }

  // إلغاء جميع الإشعارات
  Future<void> cancelAllNotifications() async {
    await _notificationsPlugin.cancelAll();
    print('✅ تم إلغاء جميع الإشعارات');
  }

  // الحصول على الإشعارات المجدولة
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _notificationsPlugin.pendingNotificationRequests();
  }

  // طباعة الإشعارات المجدولة للتشخيص
  Future<void> printPendingNotifications() async {
    final pending = await getPendingNotifications();
    print('📋 الإشعارات المجدولة: ${pending.length}');
    
    for (final notification in pending) {
      print('  - ID: ${notification.id}, العنوان: ${notification.title}');
    }
  }
}
