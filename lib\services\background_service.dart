import 'dart:async';
import 'package:balshifa/models/dose.dart';
import 'package:balshifa/models/medicine.dart';
import 'package:balshifa/services/database_service.dart';
import 'package:balshifa/services/notification_service.dart';

class BackgroundService {
  static final BackgroundService _instance = BackgroundService._internal();
  factory BackgroundService() => _instance;
  BackgroundService._internal();

  // التحقق من الجرعات المجدولة وعرض تنبيهات
  Future<void> checkScheduledDoses() async {
    print('التحقق من الجرعات المجدولة...');
    
    // الحصول على جميع الأدوية
    final db = DatabaseService.instance;
    final patients = await db.getAllPatients();
    
    for (final patient in patients) {
      final medicines = await db.getMedicinesByPatient(patient.id!);
      
      for (final medicine in medicines) {
        final scheduledDoses = await db.getScheduledDoses(medicine.id!);
        
        for (final dose in scheduledDoses) {
          // التحقق إذا كان الوقت قد حان
          if (_isTimeForDose(dose.scheduledDateTime)) {
            // عرض إشعار
            await NotificationService().showDoseNotification(dose, medicine.name);
            
            // التحقق إذا تجاوز الوقت ساعة دون تأكيد
            _checkMissedDoseAfterHour(dose);
          }
        }
      }
    }
  }

  bool _isTimeForDose(DateTime scheduledTime) {
    final now = DateTime.now();
    final difference = scheduledTime.difference(now).inMinutes;
    
    // إذا كان الوقت قد حان أو مر أقل من 30 دقيقة
    return difference >= -30 && difference <= 30;
  }

  Future<void> _checkMissedDoseAfterHour(Dose dose) async {
    Timer(const Duration(hours: 1), () async {
      // إعادة التحقق من حالة الجرعة
      final db = DatabaseService.instance;
      final updatedDose = await _getDoseById(dose.id!);
      
      if (updatedDose != null && updatedDose.status == 'scheduled') {
        // تحديث الحالة إلى مفقودة
        final missedDose = Dose(
          id: updatedDose.id,
          medicineId: updatedDose.medicineId,
          scheduledDateTime: updatedDose.scheduledDateTime,
          status: 'missed',
        );
        
        await db.updateDose(missedDose);
      }
    });
  }

  Future<Dose?> _getDoseById(int id) async {
    final db = DatabaseService.instance;
    return await db.getDoseById(id);
  }
}
