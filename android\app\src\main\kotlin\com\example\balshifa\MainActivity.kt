package com.example.balshifa

import android.content.Intent
import android.media.AudioManager
import android.media.MediaPlayer
import android.media.RingtoneManager
import android.net.Uri
import android.os.Vibrator
import android.os.VibrationEffect
import android.os.Build
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val NOTIFICATION_CHANNEL = "notification_channel"
    private val SOUND_CHANNEL = "sound_service"
    private var alarmMediaPlayer: MediaPlayer? = null

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // قناة الإشعارات
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, NOTIFICATION_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "showNotificationActivity" -> {
                    val medicineName = call.argument<String>("medicine_name") ?: "دواء"
                    val doseTime = call.argument<String>("dose_time") ?: "الآن"
                    val notificationId = call.argument<Int>("notification_id") ?: 0

                    val intent = Intent(this, NotificationActivity::class.java).apply {
                        putExtra("medicine_name", medicineName)
                        putExtra("dose_time", doseTime)
                        putExtra("notification_id", notificationId)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    }

                    startActivity(intent)
                    result.success(true)
                }
                "stopNotificationActivity" -> {
                    // إرسال broadcast لإيقاف النشاط
                    val stopIntent = Intent("STOP_NOTIFICATION_ACTIVITY")
                    sendBroadcast(stopIntent)
                    result.success(true)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }

        // قناة الصوت
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, SOUND_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "playAlarmSound" -> {
                    playAlarmSound()
                    result.success(true)
                }
                "stopAlarmSound" -> {
                    stopAlarmSound()
                    result.success(true)
                }
                "playAlarmWithVibration" -> {
                    playAlarmWithVibration()
                    result.success(true)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun playAlarmSound() {
        try {
            stopAlarmSound() // إيقاف أي صوت سابق

            // استخدام صوت المنبه الافتراضي للنظام
            val alarmUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM)
                ?: RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
                ?: RingtoneManager.getDefaultUri(RingtoneManager.TYPE_RINGTONE)

            alarmMediaPlayer = MediaPlayer().apply {
                setDataSource(this@MainActivity, alarmUri)
                setAudioStreamType(AudioManager.STREAM_ALARM) // استخدام قناة المنبه
                isLooping = true // تكرار مستمر
                prepare()
                start()
            }

            // رفع مستوى الصوت إلى الحد الأقصى
            val audioManager = getSystemService(AUDIO_SERVICE) as AudioManager
            val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_ALARM)
            audioManager.setStreamVolume(AudioManager.STREAM_ALARM, maxVolume, 0)

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun stopAlarmSound() {
        alarmMediaPlayer?.apply {
            if (isPlaying) {
                stop()
            }
            release()
        }
        alarmMediaPlayer = null
    }

    private fun playAlarmWithVibration() {
        playAlarmSound()

        // إضافة اهتزاز قوي
        val vibrator = getSystemService(VIBRATOR_SERVICE) as Vibrator
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val vibrationEffect = VibrationEffect.createWaveform(
                longArrayOf(0, 1000, 500, 1000, 500, 1000), // نمط الاهتزاز
                0 // تكرار من البداية
            )
            vibrator.vibrate(vibrationEffect)
        } else {
            @Suppress("DEPRECATION")
            vibrator.vibrate(longArrayOf(0, 1000, 500, 1000, 500, 1000), 0)
        }
    }
}
