package com.example.balshifa

import android.content.Intent
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val CHANNEL = "notification_channel"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "showNotificationActivity" -> {
                    val medicineName = call.argument<String>("medicine_name") ?: "دواء"
                    val doseTime = call.argument<String>("dose_time") ?: "الآن"
                    val notificationId = call.argument<Int>("notification_id") ?: 0

                    val intent = Intent(this, NotificationActivity::class.java).apply {
                        putExtra("medicine_name", medicineName)
                        putExtra("dose_time", doseTime)
                        putExtra("notification_id", notificationId)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    }

                    startActivity(intent)
                    result.success(true)
                }
                "stopNotificationActivity" -> {
                    // إرسال broadcast لإيقاف النشاط
                    val stopIntent = Intent("STOP_NOTIFICATION_ACTIVITY")
                    sendBroadcast(stopIntent)
                    result.success(true)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }
}
